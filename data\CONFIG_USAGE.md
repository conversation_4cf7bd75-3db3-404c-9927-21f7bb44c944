# Database Configuration Usage

The `setup_database.py` script supports two methods for loading database configuration:

## Method 1: YAML Configuration (Default)

The script uses `config.yml` by default. Make sure your `config.yml` file has the following structure:

```yaml
database:
  name: "sql_judge_db"
  user: "your_db_user"
  password: "your_db_password"
  host: "localhost"
  port: "5432"
```

## Method 2: Environment Variables

To use environment variables instead:

1. Change the `CONFIG_TYPE` variable in `setup_database.py`:

   ```python
   CONFIG_TYPE = 'env'  # Change from 'yaml' to 'env'
   ```

2. Create a `.env` file in the project root (you can copy from `.env.example`):
   ```
   DB_NAME=sql_judge_db
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_HOST=localhost
   DB_PORT=5432
   ```

## Switching Between Methods

Simply change the `CONFIG_TYPE` variable in `setup_database.py`:

- `CONFIG_TYPE = 'yaml'` - Uses config.yml file
- `CONFIG_TYPE = 'env'` - Uses environment variables (.env file or system environment)

## Using in Other Python Files

For other Python files that need database configuration, you can use the clean `config_utils` module:

### Method 1: Direct configuration loading (Recommended)

```python
import sys
from pathlib import Path

# Add root directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))
from config_utils import load_db_config

# Load configuration directly
db_config = load_db_config('yaml')  # or 'env'
```

### Method 2: Using a loader instance

```python
import sys
from pathlib import Path

# Add root directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))
from config_utils import get_loader

# Get a loader and use it
loader = get_loader('yaml')
db_config = loader.load_db_config()
```

### Method 3: One-liner for simple cases

```python
import sys; from pathlib import Path; sys.path.insert(0, str(Path(__file__).parent.parent))
from config_utils import load_db_config
db_config = load_db_config('yaml')
```

## Dependencies

- For YAML support: `pyyaml`
- For .env support: `python-dotenv`

Both methods validate that required database credentials are present and will raise an error if any are missing.
