# sql_evaluater/judge.py
from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI

# 1. Define the desired structured output using Pydantic


class SQLEvaluation(BaseModel):
    """Structured format for the SQL query evaluation."""
    correctness_score: int = Field(
        description="Score from 1 (poor) to 5 (excellent) for logical correctness")
    correctness_reasoning: str = Field(
        description="Detailed reasoning for the correctness score")

    optimization_score: int = Field(
        description="Score from 1 (poor) to 5 (excellent) for performance and efficiency")
    optimization_reasoning: str = Field(
        description="Detailed reasoning for the optimization score")

    is_safe: bool = Field(
        description="True if the query is safe, False if it contains potential threats")
    security_reasoning: str = Field(
        description="Explanation of any potential security threats like DROP, DELETE without WHERE, etc.")

    final_verdict: str = Field(
        description="A final 'PASS' or 'FAIL' verdict based on the overall evaluation")


# 2. Implement the Judge class
class GeminiSQLJudge:
    def __init__(self, config: Dict[str, Any]):
        """Initializes the Gemini Judge with JSON output parsing."""
        self.llm = ChatGoogleGenerativeAI(
            model=config['model_name'],
            temperature=config['temperature'],
            convert_system_message_to_human=True  # Important for Gemini
        )
        self.prompt_template = self._get_prompt_template()

    def _get_prompt_template(self) -> ChatPromptTemplate:
        """Creates the detailed evaluation prompt (the rubric)."""
        return ChatPromptTemplate.from_messages([
            ("system",
             """You are an expert database administrator and senior data analyst. Your task is to meticulously evaluate a generated SQL query based on a user's request and a database schema.

             You must evaluate across three criteria: Correctness, Optimization, and Security.
             Provide your evaluation in JSON format with the following fields:

             - correctness_score: Integer from 1-5 for logical correctness
             - correctness_reasoning: String with detailed reasoning for the correctness score
             - optimization_score: Integer from 1-5 for performance and efficiency
             - optimization_reasoning: String with detailed reasoning for the optimization score
             - is_safe: Boolean indicating if the query is safe
             - security_reasoning: String explaining any security concerns
             - final_verdict: String either 'PASS' or 'FAIL' based on overall evaluation

             Evaluation Criteria Breakdown:
             1.  **Correctness (Score 1-5):**
                 - Does the query correctly answer the user's question?
                 - Does it select the right columns and join the correct tables?
                 - Are the filtering conditions (`WHERE` clauses) accurate?
             2.  **Optimization (Score 1-5):**
                 - Is the query efficient? Does it avoid unnecessary subqueries or complex operations where a simpler JOIN would suffice?
                 - Are indexes likely to be used effectively (hypothetically)?
                 - 5 is highly optimal, 1 is extremely inefficient.
             3.  **Security (is_safe: true/false):**
                 - Does the query pose a threat? Look for `DROP`, `TRUNCATE`, `DELETE` without a `WHERE` clause, or other destructive operations.
                 - Read-only `SELECT` queries are generally safe.
             """),
            ("human",
             """
             Here is the information for your evaluation:

             **Database Schema:**
             ```sql
             {schema}
             ```

             **User's Original Question:**
             "{user_prompt}"

             **Generated SQL Query to Evaluate:**
             ```sql
             {generated_sql}
             ```

             Please provide your detailed evaluation now in JSON format only. Do not include any other text.

             Example format:
             ```json
             {{
               "correctness_score": 4,
               "correctness_reasoning": "The query correctly selects from the right table...",
               "optimization_score": 5,
               "optimization_reasoning": "The query is efficient with proper LIMIT clause...",
               "is_safe": true,
               "security_reasoning": "This is a safe SELECT query with no destructive operations",
               "final_verdict": "PASS"
             }}
             ```
             """)
        ])

    def evaluate(self, schema: str, user_prompt: str, generated_sql: str) -> SQLEvaluation:
        """Runs the evaluation chain with JSON parsing."""
        import json
        from langchain_core.output_parsers import StrOutputParser

        # Create the chain
        chain = self.prompt_template | self.llm | StrOutputParser()

        # Get the response
        response = chain.invoke({
            "schema": schema,
            "user_prompt": user_prompt,
            "generated_sql": generated_sql
        })

        try:
            # Try to extract JSON from the response
            # Sometimes the LLM wraps JSON in markdown code blocks
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "```" in response:
                json_start = response.find("```") + 3
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()

            # Parse JSON
            json_data = json.loads(json_str)
            return SQLEvaluation(**json_data)

        except (json.JSONDecodeError, Exception) as e:
            print(f"⚠️  Warning: Could not parse JSON response: {e}")
            print(f"📝 Raw response: {response[:200]}...")
            # Return a default evaluation
            return SQLEvaluation(
                correctness_score=3,
                correctness_reasoning="Could not evaluate due to parsing error",
                optimization_score=3,
                optimization_reasoning="Could not evaluate due to parsing error",
                is_safe=True,
                security_reasoning="Assumed safe due to parsing error",
                final_verdict="UNKNOWN"
            )
