# run_evaluation.py
from sql_judge_agent.judge import GeminiS<PERSON><PERSON><PERSON>, SQLEvaluation
from sql_judge_agent.utils import get_db_schema
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.output_parsers import <PERSON>r<PERSON>utput<PERSON>arser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from config_utils import load_db_config, load_full_config
import sys
import pprint
from pathlib import Path

# Add root directory to Python path for clean imports
sys.path.insert(0, str(Path(__file__).parent))


# --- Main Execution ---
if __name__ == "__main__":
    # 1. Load configuration from config.yml
    config = load_full_config()
    print("✅ Configuration loaded from config.yml")

    # 2. Define the user's natural language question
    # --- Try changing this prompt to test different scenarios! ---
    user_prompt = "Return the first 5 rows from the bkr users table"

    print("======================================================================")
    print(f"▶️  Starting Evaluation for Prompt: '{user_prompt}'")
    print("======================================================================")

    # 3. Get Database Schema
    print("\nSTEP 1: Fetching database schema...")
    # Load database configuration from config.yml (using config loaders)
    db_config = load_db_config('yaml')
    db_schema = get_db_schema(db_config)

    if not db_schema:
        print("❌ Halting execution due to database schema fetch failure.")
        exit()

    # 4. Generate the SQL Query (The "Performer" LLM)
    print("\nSTEP 2: Generating SQL query with the Performer LLM...")
    performer_config = config['performer_llm']
    performer_llm = ChatGoogleGenerativeAI(
        model=performer_config['model_name'],
        temperature=performer_config['temperature']
    )

    performer_prompt = ChatPromptTemplate.from_messages([
        ("system", "You are an expert SQL writer. Given a database schema and a user question, write a single, syntactically correct SQL query to answer the question. Only output the SQL query."),
        ("human", "Schema:\n{schema}\n\nQuestion: {question}")
    ])

    performer_chain = performer_prompt | performer_llm | StrOutputParser()

    generated_sql = performer_chain.invoke({
        "schema": db_schema,
        "question": user_prompt
    })

    print("✅ Performer LLM generated the following SQL:")
    print("----------------------------------------------------------------------")
    print(generated_sql)
    print("----------------------------------------------------------------------")

    # 5. Evaluate the Generated SQL (The "Judge" LLM)
    print("\nSTEP 3: Evaluating generated SQL with the Judge LLM...")
    judge = GeminiSQLJudge(config['judge_llm'])

    try:
        evaluation_result = judge.evaluate(
            schema=db_schema,
            user_prompt=user_prompt,
            generated_sql=generated_sql
        )
        print("✅ Judge LLM provided the following evaluation:")
        print("----------------------------------------------------------------------")
        # Pretty print the Pydantic object
        pprint.pprint(evaluation_result.model_dump())
        print("----------------------------------------------------------------------")

    except Exception as e:
        print(f"❌ An error occurred during evaluation: {e}")

    print("\n✅ Evaluation complete.")
